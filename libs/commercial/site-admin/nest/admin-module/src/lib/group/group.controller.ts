import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ChargeService } from '@experience/commercial/site-admin/nest/charge-module';
import {
  Controller,
  Get,
  Query,
  Res,
  SerializeOptions,
  StreamableFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  Group,
  defaultChargeCsvColumns,
  generateChargesCsvFileName,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { GroupId } from '../admin-context.decorator';
import { GroupService } from './group.service';
import { Response } from 'express';
import { generateCsvFile } from '@experience/shared/nest/utils';

@Controller('user')
@SerializeOptions({ exposeUnsetFields: false })
export class GroupController {
  constructor(
    private groupService: GroupService,
    private chargeService: ChargeService
  ) {}

  @Get('group')
  @CacheTTL(60000)
  @UseInterceptors(CacheInterceptor)
  async findByGroupId(@GroupId() groupId: number): Promise<Group> {
    return this.groupService.findByGroupIdWithStats(groupId);
  }

  @Get('groups')
  async findAllByGroupId(@GroupId() groupId: number): Promise<Group[]> {
    return this.groupService.findAllByGroupId(groupId);
  }

  @Get('group/charges/csv')
  async generateCsv(
    @GroupId() groupId: number,
    @Res({ passthrough: true }) res: Response,
    @Query('date')
    date?: string
  ): Promise<StreamableFile> {
    const group = await this.groupService.findByGroupId(groupId);

    const charges =
      await this.chargeService.findChargeDataFromProjectionsEndpoint({
        date: date,
        groupId: group.id,
        groupUid: group.uid,
        sortBy: 'siteNamePodNamePluggedIn',
      });

    const { vehicle, ...defaultColumns } = defaultChargeCsvColumns;

    const columns = {
      siteName: 'Site name',
      ...defaultColumns,
    };

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${generateChargesCsvFileName(
        group.name,
        date
      )}`,
    });

    return generateCsvFile(columns, charges, {
      boolean: ['confirmed'],
      time: ['chargingDuration', 'totalDuration'],
    });
  }
}
