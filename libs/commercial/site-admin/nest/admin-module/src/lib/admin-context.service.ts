import { Injectable, Scope } from '@nestjs/common';

export interface AdminContext {
  adminId?: number;
  adminUid?: string;
  groupId?: number;
  groupUid?: string;
}

@Injectable({ scope: Scope.REQUEST })
export class AdminContextService {
  private context: AdminContext = {};

  setAdminId(adminId: number): void {
    this.context.adminId = adminId;
  }

  getAdminId(): number {
    if (this.context.adminId === undefined) {
      throw new Error('Admin ID not set in context');
    }
    return this.context.adminId;
  }

  setAdminUid(adminUid: string): void {
    this.context.adminUid = adminUid;
  }

  getAdminUid(): string {
    if (this.context.adminUid === undefined) {
      throw new Error('Admin UID not set in context');
    }
    return this.context.adminUid;
  }

  setGroupId(groupId: number): void {
    this.context.groupId = groupId;
  }

  getGroupId(): number {
    if (this.context.groupId === undefined) {
      throw new Error('Group ID not set in context');
    }
    return this.context.groupId;
  }

  setGroupUid(groupUid: string): void {
    this.context.groupUid = groupUid;
  }

  getGroupUid(): string {
    if (this.context.groupUid === undefined) {
      throw new Error('Group UID not set in context');
    }
    return this.context.groupUid;
  }

  getContext(): AdminContext {
    return { ...this.context };
  }

  clear(): void {
    this.context = {};
  }
}
