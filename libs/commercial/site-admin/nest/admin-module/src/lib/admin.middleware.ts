import {
  AMAZON_OIDC_DATA_HEADER,
  getOidcUserOrThrow,
} from '@experience/shared/typescript/oidc-utils';
import { AdminService } from './admin.service';
import { AdminStatus } from '@experience/commercial/site-admin/typescript/domain-model';
import { GroupService } from './group/group.service';
import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { AdminContextService } from './admin-context.service';

@Injectable()
export class AdminMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AdminMiddleware.name);

  constructor(
    private adminService: AdminService,
    private groupService: GroupService,
    private adminContextService: AdminContextService
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    this.logger.log({}, 'authorising request');

    // Attach context service to request for decorators
    (req as any).adminContextService = this.adminContextService;

    const hasAuthId = !!req.query.authId;
    const hasOidcData = !!req.header(AMAZON_OIDC_DATA_HEADER);

    if (!hasAuthId && !hasOidcData) {
      this.logger.log('no auth id or oidc data present on request');
      next();
      return;
    }

    // check for the presence of an admin matching the auth id or oidc data on the request
    const admin = hasAuthId
      ? await this.adminService.findByAuthId(req.query.authId as string)
      : await this.adminService.findByEmail(getOidcUserOrThrow(req).email);

    if (!admin) {
      this.logger.warn({ req }, 'no admin found');
      res.sendStatus(401);
      return;
    }

    // set up context to be used in all subsequent logging
    const loggerContext = {
      authId: admin.authId,
      email: admin.email,
      groupId: admin.groupId,
      groupName: admin.groupName,
      groupUid: admin.groupUid,
    };

    // check for the presence of a group id on the admin
    if (!admin.groupId || !admin.groupUid) {
      this.logger.log(loggerContext, 'admin has no group');
      res.sendStatus(401);
      return;
    }

    // automatically activate new admins
    if (admin.status === AdminStatus.PENDING) {
      this.logger.log(loggerContext, 'admin has not been activated');
      await this.adminService.activateByGroupIdAndAdminId(
        admin.groupId,
        admin.id
      );
    }

    // capture group override from request
    const groupIdOverride = req.query.groupId as string;
    const groupUidOverride = req.query.groupUid as string;

    // populate admin context
    this.adminContextService.setAdminId(admin.id);
    this.adminContextService.setAdminUid(admin.authId);

    // populate group context
    this.adminContextService.setGroupId(admin.groupId);
    this.adminContextService.setGroupUid(admin.groupUid);

    // populate group override on request if permitted
    if (groupIdOverride || groupUidOverride) {
      const group = await this.groupService
        .findAllByGroupId(admin.groupId)
        .then((groups) =>
          groups.find(
            (group) =>
              group.id.toString() === groupIdOverride ||
              group.uid.toString() === groupUidOverride
          )
        );
      if (group) {
        loggerContext.groupId = group.id;
        loggerContext.groupName = group.name;
        loggerContext.groupUid = group.uid;
        this.adminContextService.setGroupId(group.id);
        this.adminContextService.setGroupUid(group.uid);
      }
    }

    const group = await this.groupService.findByGroupId(
      this.adminContextService.getGroupId()
    );

    if (group.readOnly && req.method !== 'GET') {
      this.logger.log(loggerContext, 'group is in read-only mode');
      res.sendStatus(403);
      return;
    }

    this.logger.log(loggerContext, 'request authorised');

    // forward the request to be handled by the appropriate controller
    next();
  }
}
