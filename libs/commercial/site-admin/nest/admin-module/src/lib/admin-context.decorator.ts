import { ExecutionContext, createParamDecorator } from '@nestjs/common';
import { AdminContextService } from './admin-context.service';

export const AdminId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): number => {
    const request = ctx.switchToHttp().getRequest();
    const adminContextService: AdminContextService = request.adminContextService;
    if (!adminContextService) {
      throw new Error('AdminContextService not found in request');
    }
    return adminContextService.getAdminId();
  }
);

export const AdminUid = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    const adminContextService: AdminContextService = request.adminContextService;
    if (!adminContextService) {
      throw new Error('AdminContextService not found in request');
    }
    return adminContextService.getAdminUid();
  }
);

export const GroupId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): number => {
    const request = ctx.switchToHttp().getRequest();
    const adminContextService: AdminContextService = request.adminContextService;
    if (!adminContextService) {
      throw new Error('AdminContextService not found in request');
    }
    return adminContextService.getGroupId();
  }
);

export const GroupUid = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    const adminContextService: AdminContextService = request.adminContextService;
    if (!adminContextService) {
      throw new Error('AdminContextService not found in request');
    }
    return adminContextService.getGroupUid();
  }
);
