import { AdminContextService } from './admin-context.service';
import { AdminMiddleware } from './admin.middleware';
import { AdminService } from './admin.service';
import {
  AdminStatus,
  Group,
  TEST_ADMINISTRATOR,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { GroupService } from './group/group.service';
import { NextFunction, Request, Response } from 'express';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('./admin.service');
jest.mock('./group/group.service');

describe('AdminMiddleware', () => {
  let middleware: AdminMiddleware;
  let adminService: AdminService;
  let groupService: GroupService;
  let adminContextService: AdminContextService;

  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminMiddleware,
        AdminContextService,
        {
          provide: AdminService,
          useClass: AdminService,
        },
        {
          provide: GroupService,
          useClass: GroupService,
        },
      ],
    }).compile();

    middleware = module.get<AdminMiddleware>(AdminMiddleware);
    adminService = module.get<AdminService>(AdminService);
    groupService = module.get<GroupService>(GroupService);
    adminContextService = module.get<AdminContextService>(AdminContextService);

    req = {
      header: jest.fn(),
    };
    res = {
      sendStatus: jest.fn(),
    };
    next = jest.fn();
  });

  it('should be defined', () => {
    expect(middleware).toBeDefined();
    expect(adminService).toBeDefined();
    expect(groupService).toBeDefined();
    expect(adminContextService).toBeDefined();
  });

  it('should should call next function if auth id is not present on request', async () => {
    req.query = {};

    await middleware.use(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
  });

  it('should return 401 if auth id does not correspond to an admin', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce(null as never);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expectNextNotToHaveBeenCalledAndUnauthorisedResponseStatusToHaveBeenSet();
  });

  it('should return 401 if auth id does not correspond to an admin with a group', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: undefined,
        groupName: undefined,
        groupUid: undefined,
      });

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expectNextNotToHaveBeenCalledAndUnauthorisedResponseStatusToHaveBeenSet();
  });

  it('should return 401 if auth id corresponds to an admin with a group which has been deactivated', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupName: undefined,
        groupUid: undefined,
      });

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expectNextNotToHaveBeenCalledAndUnauthorisedResponseStatusToHaveBeenSet();
  });

  it('should call next function if auth id corresponds to an admin with a group', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(100);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '100',
      'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it('should call next function if auth id corresponds to a new admin with a group', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
        status: AdminStatus.PENDING,
      });
    const mockActivateByGroupIdAndAdminId = jest
      .spyOn(adminService, 'activateByGroupIdAndAdminId')
      .mockResolvedValueOnce(void 0);
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(100);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '100',
      'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
    expect(mockActivateByGroupIdAndAdminId).toHaveBeenCalledWith(
      100,
      TEST_ADMINISTRATOR.id
    );
  });

  it('should allow group to be overridden if user has access to group', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
      groupId: '200',
      groupUid: '191ab073-f132-4a3b-968f-1771a26e73ba',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    jest.spyOn(groupService, 'findAllByGroupId').mockResolvedValue([
      {
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      } as Group,
      {
        id: 200,
        uid: '191ab073-f132-4a3b-968f-1771a26e73ba',
      } as Group,
    ]);
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 200,
        uid: '191ab073-f132-4a3b-968f-1771a26e73ba',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(200);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '200',
      '191ab073-f132-4a3b-968f-1771a26e73ba',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it('should not allow group to be overridden if user does not have access to group', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
      groupId: '200',
      groupUid: '191ab073-f132-4a3b-968f-1771a26e73ba',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    jest.spyOn(groupService, 'findAllByGroupId').mockResolvedValue([
      {
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      } as Group,
    ]);
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(100);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '100',
      'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it('should allow group to be overridden if only group uid is valid', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
      groupId: '300',
      groupUid: '191ab073-f132-4a3b-968f-1771a26e73ba',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    jest.spyOn(groupService, 'findAllByGroupId').mockResolvedValue([
      {
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      } as Group,
      {
        id: 200,
        uid: '191ab073-f132-4a3b-968f-1771a26e73ba',
      } as Group,
    ]);
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 300,
        uid: '191ab073-f132-4a3b-968f-1771a26e73ba',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(300);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '300',
      '191ab073-f132-4a3b-968f-1771a26e73ba',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it('should allow group to be overridden if only group id is valid', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
      groupId: '200',
      groupUid: '3383733c-270c-4f64-8cda-6c78bfe69bd0',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    jest.spyOn(groupService, 'findAllByGroupId').mockResolvedValue([
      {
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      } as Group,
      {
        id: 200,
        uid: '191ab073-f132-4a3b-968f-1771a26e73ba',
      } as Group,
    ]);
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 200,
        uid: '3383733c-270c-4f64-8cda-6c78bfe69bd0',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(200);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '200',
      '3383733c-270c-4f64-8cda-6c78bfe69bd0',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it('should allow group to be overridden if group id and uid do not match', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
      groupId: '200',
      groupUid: 'ae317194-4aa4-4ffc-a2c4-266402659e28',
    };
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    jest.spyOn(groupService, 'findAllByGroupId').mockResolvedValue([
      {
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      } as Group,
      {
        id: 200,
        uid: '191ab073-f132-4a3b-968f-1771a26e73ba',
      } as Group,
      {
        id: 300,
        uid: 'ae317194-4aa4-4ffc-a2c4-266402659e28',
      } as Group,
    ]);
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 200,
        uid: 'ae317194-4aa4-4ffc-a2c4-266402659e28',
        readOnly: false,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(200);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '200',
      'ae317194-4aa4-4ffc-a2c4-266402659e28',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it('should accept a GET request if the group is in read-only mode', async () => {
    req.query = {
      authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
    };
    req.method = 'GET';
    const mockFindByAuthId = jest
      .spyOn(adminService, 'findByAuthId')
      .mockResolvedValueOnce({
        ...TEST_ADMINISTRATOR,
        groupId: 100,
        groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      });
    const mockFindByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValue({
        id: 100,
        uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
        readOnly: true,
      } as Group);

    await middleware.use(req as Request, res as Response, next);

    expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
    expect(mockFindByGroupId).toHaveBeenCalledWith(100);
    expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet(
      '100',
      'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
      TEST_ADMINISTRATOR.id.toString(),
      TEST_ADMINISTRATOR.authId
    );
  });

  it.each(['POST', 'PATCH', 'PUT', 'DELETE'])(
    'should reject a %s request if the group is in read-only mode',
    async (method) => {
      req.query = {
        authId: '47a74182-1cda-471c-ae3d-192e14a430a0',
      };
      req.method = method;
      const mockFindByAuthId = jest
        .spyOn(adminService, 'findByAuthId')
        .mockResolvedValueOnce({
          ...TEST_ADMINISTRATOR,
          groupId: 100,
          groupUid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
        });
      const mockFindByGroupId = jest
        .spyOn(groupService, 'findByGroupId')
        .mockResolvedValue({
          id: 100,
          uid: 'b4b80b47-8ff1-41cf-bffc-2d94874d5d17',
          readOnly: true,
        } as Group);

      await middleware.use(req as Request, res as Response, next);

      expect(mockFindByAuthId).toHaveBeenCalledWith(req.query.authId);
      expect(mockFindByGroupId).toHaveBeenCalledWith(100);
      expectNextNotToHaveBeenCalledAndForbiddenResponseStatusToHaveBeenSet();
    }
  );

  const expectNextToHaveBeenCalledAndAdminDetailsToHaveBeenSet = (
    groupId: string,
    groupUid: string,
    adminId: string,
    adminUid: string
  ) => {
    expect(next).toHaveBeenCalled();
    expect(res.sendStatus).not.toHaveBeenCalled();
    expect(adminContextService.getGroupId()).toEqual(Number(groupId));
    expect(adminContextService.getGroupUid()).toEqual(groupUid);
    expect(adminContextService.getAdminId()).toEqual(Number(adminId));
    expect(adminContextService.getAdminUid()).toEqual(adminUid);
  };

  const expectNextNotToHaveBeenCalledAndUnauthorisedResponseStatusToHaveBeenSet =
    () => {
      expect(next).not.toHaveBeenCalled();
      expect(res.sendStatus).toHaveBeenCalledWith(401);
    };

  const expectNextNotToHaveBeenCalledAndForbiddenResponseStatusToHaveBeenSet =
    () => {
      expect(next).not.toHaveBeenCalled();
      expect(res.sendStatus).toHaveBeenCalledWith(403);
    };
});
