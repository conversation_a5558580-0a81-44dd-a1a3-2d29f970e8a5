import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpCode,
  MaxFileSizeValidator,
  Param,
  ParseBoolPipe,
  ParseFilePipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  SerializeOptions,
  UploadedFile,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateDriverRequest,
  DeleteDriverRequest,
  Driver,
  InviteDriverRequest,
  UpdateDriverRequest,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { CreateDriverRequestFileValidator } from './driver.validator';
import { DriverInterceptor } from './driver.interceptor';
import { DriverService } from './driver.service';
import { Express } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { AdminId, GroupId } from '@experience/commercial/site-admin/nest/admin-module';
import { TransactionInterceptor } from '@experience/shared/sequelize/podadmin';
import { parse } from 'csv-parse/sync';

const ONE_MEGABYTE = 1_000_000;

@Controller('drivers')
@UseInterceptors(DriverInterceptor)
@SerializeOptions({ exposeUnsetFields: false })
export class DriverController {
  constructor(private driverService: DriverService) {}

  @Get()
  async findByGroupId(
    @GroupId() groupId: number,
    @Query('includeDeleted', new ParseBoolPipe({ optional: true }))
    includeDeleted = false
  ): Promise<Driver[]> {
    return this.driverService.findByGroupId(groupId, includeDeleted);
  }

  @Post()
  @UseInterceptors(TransactionInterceptor)
  async createByGroupId(
    @GroupId() groupId: number,
    @AdminId() adminId: number,
    @Body(ValidationPipe) request: CreateDriverRequest
  ): Promise<Driver> {
    return await this.driverService
      .createByGroupId(groupId, request)
      .then(async (driver) => {
        await this.driverService.inviteByGroupIdAndDriverId(
          groupId,
          driver.id,
          adminId,
          driver.tariffTier
        );
        return driver;
      });
  }

  @Put(':driverId')
  @HttpCode(204)
  @UseInterceptors(TransactionInterceptor)
  async updateByGroupId(
    @GroupId() groupId: number,
    @Param('driverId', ParseIntPipe) driverId: number,
    @Body(ValidationPipe) request: UpdateDriverRequest
  ): Promise<void> {
    await this.driverService.updateByGroupIdAndDriverId(
      groupId,
      driverId,
      request
    );
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'), TransactionInterceptor)
  async bulkCreateByGroupId(
    @GroupId() groupId: number,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType: 'csv',
            skipMagicNumbersValidation: true,
          }),
          new MaxFileSizeValidator({ maxSize: ONE_MEGABYTE }),
          new CreateDriverRequestFileValidator({}),
        ],
      })
    )
    file: Express.Multer.File
  ): Promise<Driver[]> {
    const requests: CreateDriverRequest[] = parse(file.buffer.toString(), {
      columns: ['firstName', 'lastName', 'email', 'tariffTier', 'canExpense'],
      comment: '#',
      skipEmptyLines: true,
      trim: true,
    });
    const drivers = [];
    for (const request of requests) {
      const driver = await this.driverService.createByGroupId(groupId, request);
      drivers.push(driver);
    }
    return Promise.all(drivers);
  }

  @Delete(':driverId')
  @HttpCode(204)
  async deleteByGroupId(
    @GroupId() groupId: number,
    @Param('driverId', ParseIntPipe) driverId: number,
    @Body(ValidationPipe) request: DeleteDriverRequest
  ): Promise<void> {
    await this.driverService.deleteByGroupIdAndDriverId(
      groupId,
      driverId,
      request
    );
  }

  @Post(':driverId/invitation')
  @HttpCode(204)
  async inviteByGroupIdAndDriverId(
    @GroupId() groupId: number,
    @AdminId() adminId: number,
    @Param('driverId', ParseIntPipe) driverId: number,
    @Body(ValidationPipe) request: InviteDriverRequest
  ): Promise<void> {
    const { tariffTier } = request;

    await this.driverService.inviteByGroupIdAndDriverId(
      groupId,
      driverId,
      adminId,
      tariffTier
    );
  }
}
