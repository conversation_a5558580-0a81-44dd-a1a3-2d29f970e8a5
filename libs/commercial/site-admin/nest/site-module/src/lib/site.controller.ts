import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Res,
  SerializeOptions,
  StreamableFile,
  UseInterceptors,
} from '@nestjs/common';
import { FeatureFlag, generateCsvFile } from '@experience/shared/nest/utils';
import { GroupUid } from '@experience/commercial/site-admin/nest/admin-module';
import { Response } from 'express';
import {
  Site,
  defaultChargeCsvColumns,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { SiteInterceptor } from './site.interceptor';
import { SiteService } from './site.service';
import dayjs from 'dayjs';

@Controller('sites')
@UseInterceptors(SiteInterceptor)
@SerializeOptions({ exposeUnsetFields: false })
export class SiteController {
  constructor(private siteService: SiteService) {}

  @Get()
  async findByGroupUid(@GroupUid() groupUid: string): Promise<Site[]> {
    return this.siteService.findByGroupUid(groupUid);
  }

  @Get(':siteId')
  async findByGroupUidAndSiteId(
    @GroupUid() groupUid: string,
    @Param('siteId', ParseIntPipe) siteId: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<Site> {
    return this.siteService.findByGroupUidAndSiteId({
      groupUid,
      siteId,
      includeRecentCharges: true,
      includeSiteSummary: true,
      useChargeProjectionEndpoints,
    });
  }

  @Get(':siteId/charges')
  async generateCsv(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Param('siteId', ParseIntPipe) siteId: number,
    @Res({ passthrough: true }) res: Response,
    @Query('date') date?: string,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<StreamableFile> {
    const { chargeData, filename } =
      await this.siteService.findChargeDataByGroupUidAndSiteId(
        groupUid,
        siteId,
        date,
        dayjs(date).year() >= 2023 && useChargeProjectionEndpoints
      );

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return generateCsvFile(defaultChargeCsvColumns, chargeData, {
      boolean: ['confirmed'],
      time: ['chargingDuration', 'totalDuration'],
    });
  }
}
